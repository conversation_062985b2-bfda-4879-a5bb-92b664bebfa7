# Ignore build directories
/target/
!**/src/main/**/target/
!**/src/test/**/target/

/build/
!**/src/main/**/build/
!**/src/test/**/build/

# Maven Wrapper
!.mvn/wrapper/maven-wrapper.jar
.mvn/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
dependency-reduced-pom.xml

# Spring Tool Suite (STS)
.apt_generated
.classpath
.factorypath
.project
.settings/
.springBeans
.sts4-cache

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# Visual Studio Code
.vscode/

# Logs & temp files
logs/
*.log
hs_err_pid*

# Ignore database & cache files
*.sqlite
*.cache

# Ignore compiled classes
*.class

# Ignore environment variables & secrets
.env
.env.local
.env.development
.env.production
.env.test
