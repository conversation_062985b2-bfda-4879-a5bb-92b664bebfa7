package com.graduation.Kotaby.service;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class CheckerServiceImpl implements CheckerService {
    private final MemorizationService memorizationService;

    public CheckerServiceImpl(MemorizationService memorizationService) {
        this.memorizationService = memorizationService;
    }

    @Override
    public List<String> check(List<String> userWords, List<String> pageWords, int userID, int page) {
        System.out.println("Checking words...");
        int lastWord = memorizationService.getLastWord(userID, page);
        int n = userWords.size();
        int m = pageWords.size();
        System.out.println(lastWord + " " + n + " " + m);
        int l = 0, r = lastWord;
        List<String> correctWords = new ArrayList<>();
        while (l < n && r < m) {
            System.out.println("Comparing: " + removeTashkeel(userWords.get(l)) + " with " + removeTashkeel(pageWords.get(r)));
            if (Objects.equals(removeTashkeel(userWords.get(l)), removeTashkeel(pageWords.get(r)))) {
                correctWords.add(userWords.get(l));
                l++;
                r++;
            } else {
                break;
            }
        }
        correctWords.forEach(System.out::println);
        return correctWords;
    }

    private String removeTashkeel(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        String result = text.replaceAll("[\u064B-\u065F\u0670\u06D6-\u06ED\u08D4-\u08E1\u08E3-\u08FF\u0640]", "");
        result = result.replace("ٱ", "ا");
        result = result.replace("أ", "ا");
        result = result.replace("إ", "ا");
        result = result.replace("آ", "ا");
        return result;
    }
}
