spring.application.name=Kotaby

# Primary Database (MySQL - Online)
spring.datasource.primary.jdbcUrl=*********************************************************************************************************************************************************
spring.datasource.primary.username=admin
spring.datasource.primary.password=KotabyPassword
spring.datasource.primary.driverClassName=com.mysql.cj.jdbc.Driver

# Secondary Database (SQLite - Local)
spring.datasource.secondary.jdbcUrl=*************************
spring.datasource.secondary.driverClassName=org.sqlite.JDBC

# Hibernate Settings
spring.jpa.primary.hibernate.ddl-auto=none
spring.jpa.primary.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.primary.show-sql=true
spring.jpa.primary.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

spring.jpa.secondary.hibernate.ddl-auto=none
spring.jpa.secondary.properties.hibernate.dialect=org.hibernate.community.dialect.SQLiteDialect
spring.jpa.secondary.show-sql=true
spring.jpa.secondary.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

# multipart file upload properties
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=10MB
logging.level.root=ERROR

# Swagger
springdoc.info.title=Kotaby API
springdoc.info.version=1.0.0
springdoc.info.description=API for managing Kotaby resources.

# Model API settings
model.api.url=https://test9-a775a1e-v1.app.beam.cloud/
model.api.token=MH9eOBlNHZkV8U8iuOj8mEE1WGdPbHKowFnG3TsG0aAmAqQTs6IeIFlB-GjN9JEz9l7OHaCTqI77Lsb5c3BlYw==